const { db } = require('../db/database');

async function checkStorageValues() {
  console.log('🔍 Checking plan storage values...\n');

  try {
    const plans = await new Promise((resolve, reject) => {
      db.all('SELECT name, max_storage_gb FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('📋 Plan storage values:');
    plans.forEach(plan => {
      console.log(`- ${plan.name}: ${plan.max_storage_gb}GB (raw value: ${plan.max_storage_gb})`);
      
      // Show how it should be formatted
      if (plan.max_storage_gb < 1) {
        console.log(`  Should display as: ${Math.round(plan.max_storage_gb * 1024)}MB`);
      } else {
        console.log(`  Should display as: ${plan.max_storage_gb}GB`);
      }
    });

    console.log('\n✅ Storage values check completed!');
  } catch (error) {
    console.error('❌ Error checking storage values:', error);
  }

  process.exit(0);
}

checkStorageValues();
