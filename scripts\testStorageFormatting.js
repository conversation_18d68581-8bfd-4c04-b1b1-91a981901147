const { db } = require('../db/database');

async function testStorageFormatting() {
  console.log('🔍 Testing storage formatting consistency...\n');

  try {
    const plans = await new Promise((resolve, reject) => {
      db.all('SELECT name, max_storage_gb, features FROM subscription_plans WHERE is_active = 1 ORDER BY price ASC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('📋 Storage formatting test results:');
    plans.forEach(plan => {
      console.log(`\n📦 ${plan.name} Plan:`);
      console.log(`   Raw storage value: ${plan.max_storage_gb}GB`);
      
      // Test the formatting logic
      if (plan.max_storage_gb < 1) {
        const mbValue = Math.round(plan.max_storage_gb * 1024);
        console.log(`   ✅ Should display as: ${mbValue}MB`);
        console.log(`   📱 Plan cards: "${mbValue}MB Storage"`);
        console.log(`   📊 Dashboard: "${mbValue}MB"`);
        console.log(`   📋 Table: "${mbValue}MB"`);
      } else {
        console.log(`   ✅ Should display as: ${plan.max_storage_gb}GB`);
        console.log(`   📱 Plan cards: "${plan.max_storage_gb}GB Storage"`);
        console.log(`   📊 Dashboard: "${plan.max_storage_gb}GB"`);
        console.log(`   📋 Table: "${plan.max_storage_gb}GB"`);
      }

      // Test features
      const features = JSON.parse(plan.features || '[]');
      console.log(`   🎯 Features: ${features.length > 0 ? features.join(', ') : 'Basic Support (default)'}`);
    });

    console.log('\n✅ Storage formatting test completed!');
    console.log('📝 All interfaces should now show consistent storage formatting:');
    console.log('   - Preview plan: 150MB (not 0.146484375GB)');
    console.log('   - Basic plan: 5GB');
    console.log('   - Features: Actual plan features or "Basic Support" fallback');

  } catch (error) {
    console.error('❌ Error testing storage formatting:', error);
  }

  process.exit(0);
}

testStorageFormatting();
