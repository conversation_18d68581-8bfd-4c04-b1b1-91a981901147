class CacheService {
  constructor() {
    this.cache = new Map();
    this.ttlMap = new Map();
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
    this.maxSize = 1000; // Maximum cache entries
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0
    };

    // Start cleanup interval
    this.startCleanupInterval();
  }

  // Set cache with TTL
  set(key, value, ttl = this.defaultTTL) {
    try {
      // Check if cache is full
      if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
        this.evictOldest();
      }

      const expiresAt = Date.now() + ttl;
      
      this.cache.set(key, {
        value,
        createdAt: Date.now(),
        accessedAt: Date.now(),
        accessCount: 0
      });
      
      this.ttlMap.set(key, expiresAt);
      this.stats.sets++;

      console.log(`[Cache] Set key: ${key} (TTL: ${ttl}ms)`);
      return true;
    } catch (error) {
      console.error('[Cache] Error setting cache:', error);
      return false;
    }
  }

  // Get cache value
  get(key) {
    try {
      // Check if key exists and not expired
      if (!this.cache.has(key) || this.isExpired(key)) {
        this.stats.misses++;
        if (this.cache.has(key)) {
          this.delete(key); // Clean up expired key
        }
        return null;
      }

      const entry = this.cache.get(key);
      entry.accessedAt = Date.now();
      entry.accessCount++;
      
      this.stats.hits++;
      console.log(`[Cache] Hit key: ${key}`);
      return entry.value;
    } catch (error) {
      console.error('[Cache] Error getting cache:', error);
      this.stats.misses++;
      return null;
    }
  }

  // Delete cache entry
  delete(key) {
    try {
      const deleted = this.cache.delete(key);
      this.ttlMap.delete(key);
      
      if (deleted) {
        this.stats.deletes++;
        console.log(`[Cache] Deleted key: ${key}`);
      }
      
      return deleted;
    } catch (error) {
      console.error('[Cache] Error deleting cache:', error);
      return false;
    }
  }

  // Check if key is expired
  isExpired(key) {
    const expiresAt = this.ttlMap.get(key);
    return expiresAt && Date.now() > expiresAt;
  }

  // Clear all cache
  clear() {
    const size = this.cache.size;
    this.cache.clear();
    this.ttlMap.clear();
    console.log(`[Cache] Cleared ${size} entries`);
  }

  // Evict oldest entry
  evictOldest() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessedAt < oldestTime) {
        oldestTime = entry.accessedAt;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
      this.stats.evictions++;
      console.log(`[Cache] Evicted oldest key: ${oldestKey}`);
    }
  }

  // Start cleanup interval for expired entries
  startCleanupInterval() {
    setInterval(() => {
      this.cleanupExpired();
    }, 60000); // Run every minute
  }

  // Clean up expired entries
  cleanupExpired() {
    let cleanedCount = 0;
    const now = Date.now();

    for (const [key, expiresAt] of this.ttlMap.entries()) {
      if (now > expiresAt) {
        this.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`[Cache] Cleaned up ${cleanedCount} expired entries`);
    }
  }

  // Get cache statistics
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      size: this.cache.size,
      maxSize: this.maxSize,
      memoryUsage: this.getMemoryUsage()
    };
  }

  // Estimate memory usage
  getMemoryUsage() {
    let totalSize = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      totalSize += this.estimateSize(key) + this.estimateSize(entry);
    }
    
    return {
      estimated: `${(totalSize / 1024).toFixed(2)} KB`,
      entries: this.cache.size
    };
  }

  // Estimate object size in bytes
  estimateSize(obj) {
    const jsonString = JSON.stringify(obj);
    return new Blob([jsonString]).size;
  }

  // Cache wrapper for database queries
  async cacheQuery(key, queryFunction, ttl = this.defaultTTL) {
    try {
      // Try to get from cache first
      const cached = this.get(key);
      if (cached !== null) {
        return cached;
      }

      // Execute query and cache result
      const result = await queryFunction();
      this.set(key, result, ttl);
      
      return result;
    } catch (error) {
      console.error('[Cache] Error in cacheQuery:', error);
      // Return query result without caching on error
      return await queryFunction();
    }
  }

  // Invalidate cache by pattern
  invalidatePattern(pattern) {
    let deletedCount = 0;
    const regex = new RegExp(pattern);

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.delete(key);
        deletedCount++;
      }
    }

    console.log(`[Cache] Invalidated ${deletedCount} keys matching pattern: ${pattern}`);
    return deletedCount;
  }

  // Preload common data
  async preloadCommonData() {
    try {
      console.log('[Cache] Preloading common data...');
      
      // Preload system stats (frequently accessed)
      const systemMonitor = require('./systemMonitor');
      const systemStats = await systemMonitor.getSystemStats();
      this.set('system:stats', systemStats, 30000); // 30 seconds TTL

      // Preload subscription plans
      const Subscription = require('../models/Subscription');
      const plans = await Subscription.getAllPlans();
      this.set('subscription:plans', plans, 300000); // 5 minutes TTL

      console.log('✅ Common data preloaded to cache');
    } catch (error) {
      console.error('❌ Error preloading common data:', error);
    }
  }

  // Cache keys for different data types
  static keys = {
    user: (id) => `user:${id}`,
    userStats: (id) => `user:stats:${id}`,
    userStreams: (id) => `user:streams:${id}`,
    userVideos: (id) => `user:videos:${id}`,
    stream: (id) => `stream:${id}`,
    streamWithVideo: (id) => `stream:video:${id}`,
    video: (id) => `video:${id}`,
    systemStats: () => 'system:stats',
    adminStats: () => 'admin:stats',
    subscriptionPlans: () => 'subscription:plans',
    loadBalancerStatus: () => 'loadbalancer:status',
    loadBalancerMetrics: () => 'loadbalancer:metrics'
  };

  // Invalidate user-related cache
  invalidateUser(userId) {
    this.invalidatePattern(`user:${userId}:.*`);
    this.delete(CacheService.keys.user(userId));
    this.delete(CacheService.keys.userStats(userId));
    this.delete(CacheService.keys.userStreams(userId));
    this.delete(CacheService.keys.userVideos(userId));
  }

  // Invalidate stream-related cache
  invalidateStream(streamId, userId = null) {
    this.delete(CacheService.keys.stream(streamId));
    this.delete(CacheService.keys.streamWithVideo(streamId));
    
    if (userId) {
      this.delete(CacheService.keys.userStreams(userId));
      this.delete(CacheService.keys.userStats(userId));
    }
  }

  // Invalidate video-related cache
  invalidateVideo(videoId, userId = null) {
    this.delete(CacheService.keys.video(videoId));
    
    if (userId) {
      this.delete(CacheService.keys.userVideos(userId));
      this.delete(CacheService.keys.userStats(userId));
    }
  }

  // Invalidate system cache
  invalidateSystem() {
    this.delete(CacheService.keys.systemStats());
    this.delete(CacheService.keys.adminStats());
    this.delete(CacheService.keys.loadBalancerStatus());
    this.delete(CacheService.keys.loadBalancerMetrics());
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
